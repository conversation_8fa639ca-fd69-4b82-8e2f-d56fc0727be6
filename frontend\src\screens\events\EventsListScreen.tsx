import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
  TextInput,
  ScrollView,
  StatusBar,
  Platform,
} from 'react-native';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useTranslation } from 'react-i18next';
import type { Event } from '../../types';
import { eventsService } from '@services/events';
import type { EventsStackParamList } from '@navigation/types';
import { getTextAlign } from '@utils/rtl';
import Header from '@components/Header';

type EventsListScreenNavigationProp = StackNavigationProp<EventsStackParamList, 'EventsList'>;

export default function EventsListScreen() {
  const { t } = useTranslation();
  const navigation = useNavigation<EventsListScreenNavigationProp>();
  const [events, setEvents] = useState<Event[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    loadEvents();
  }, []);

  // Refresh events when screen comes into focus (e.g., after creating a new event)
  useFocusEffect(
    React.useCallback(() => {
      console.log('🔄 EventsList screen focused - refreshing events...');
      loadEvents();
    }, [])
  );

  const loadEvents = async () => {
    try {
      console.log('🔄 Loading events...');
      const fetchedEvents = await eventsService.getEvents();
      setEvents(fetchedEvents);
      console.log('✅ Events loaded successfully:', fetchedEvents.length);
    } catch (error) {
      console.error('❌ Error loading events:', error);
      // Fallback to mock data if both backend and Supabase fail
      const mockEvents: Event[] = [
        {
          id: '1',
          title: 'יריד קיץ קהילתי',
          description: 'יריד קהילתי שנתי עם משחקים, אוכל ומוכרים מקומיים. מושלם למשפחות!',
          type: 'community',
          createdBy: 'admin',
          date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
          location: 'הפארק המרכזי',
          rsvp: [],
          comments: [],
          reactions: [],
          createdAt: new Date(),
        },
        {
          id: '2',
          title: 'סדנת גינון קהילתי',
          description: 'למדו טכניקות גינון בר-קיימא עם המומחים המקומיים שלנו.',
          type: 'community',
          createdBy: 'user123',
          date: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000),
          location: 'מרכז הגינון',
          rsvp: [],
          comments: [],
          reactions: [],
          createdAt: new Date(),
        },
        {
          id: '3',
          title: 'טורניר כדורגל נוער',
          description: 'טורניר כדורגל לילדים מקומיים. בואו לעודד את הספורטאים הצעירים שלנו!',
          type: 'community',
          createdBy: 'admin',
          date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
          location: 'מגרש הספורט',
          rsvp: [],
          comments: [],
          reactions: [],
          createdAt: new Date(),
        },
        {
          id: '4',
          title: 'לילת קולנוע תחת הכוכבים',
          description: 'הקרנת סרט חיצונית ידידותית למשפחה. הביאו שמיכות וחטיפים.',
          type: 'community',
          createdBy: 'user456',
          date: new Date(Date.now() + 17 * 24 * 60 * 60 * 1000),
          location: 'כיכר העיר',
          rsvp: [],
          comments: [],
          reactions: [],
          createdAt: new Date(),
        },
      ];
      setEvents(mockEvents);
      console.log('⚠️ Using mock data due to API failure');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadEvents();
  };

  const handleAddEvent = () => {
    navigation.navigate('CreateEvent');
  };

  const handleEventPress = (eventId: string) => {
    navigation.navigate('EventDetails', { eventId });
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('he-IL', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(date);
  };

  const formatDateBox = (date: Date) => {
    const day = date.getDate().toString();
    const month = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
    return { day, month };
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('he-IL', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });
  };

  const filteredEvents = events.filter(event =>
    event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    event.description.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Color palette for event date boxes (matching mockup)
  const dateBoxColors = [
    '#4ecdc4', // Teal/cyan
    '#ff6b6b', // Red/coral
    '#f9ca24', // Yellow/orange
    '#a55eea', // Purple
    '#26de81', // Green
    '#fd79a8', // Pink
    '#fdcb6e', // Orange
    '#6c5ce7', // Blue-purple
  ];

  const renderEventItem = ({ item, index }: { item: Event; index: number }) => {
    const dateBox = formatDateBox(item.date);
    const time = formatTime(item.date);
    const dateBoxColor = dateBoxColors[index % dateBoxColors.length];

    return (
      <TouchableOpacity
        style={styles.eventCard}
        onPress={() => handleEventPress(item.id)}
      >
        <View style={[styles.eventDateBox, { backgroundColor: dateBoxColor }]}>
          <Text style={styles.eventDay}>{dateBox.day}</Text>
          <Text style={styles.eventMonth}>{dateBox.month}</Text>
        </View>

        <View style={styles.cardContent}>
          <Text style={styles.eventTitle}>{item.title}</Text>
          <Text style={styles.eventDescription}>{item.description}</Text>

          <View style={styles.eventMeta}>
            <Text style={styles.metaText}>🕐 {time}</Text>
            <Text style={styles.metaText}>📍 {item.location}</Text>
            <Text style={styles.metaText}>👥 {item.attendees || 0} going</Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text>טוען אירועים...</Text>
      </View>
    );
  }

  const addButton = (
    <TouchableOpacity
      style={styles.addButton}
      onPress={handleAddEvent}
    >
      <Text style={styles.addButtonText}>+ הוסף</Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      <Header
        title="📅 אירועי קהילה"
        showBackButton={true}
        rightComponent={addButton}
      />

      {/* Content */}
      <View style={styles.content}>
        {/* Search Bar */}
        <TextInput
          style={styles.searchBar}
          placeholder="🔍 חפש אירועים..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />

        {/* Events List */}
        <FlatList
          data={filteredEvents}
          renderItem={({ item, index }) => renderEventItem({ item, index })}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      </View>

      {/* FAB */}
      <TouchableOpacity style={styles.fab} onPress={handleAddEvent}>
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },

  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  addButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  searchBar: {
    backgroundColor: 'white',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
    fontSize: 16,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    textAlign: 'right',
  },
  listContainer: {
    paddingBottom: 100,
  },
  eventCard: {
    backgroundColor: 'white',
    borderRadius: 15,
    padding: 20,
    marginBottom: 15,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 3,
  },
  eventDateBox: {
    borderRadius: 8,
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 15,
    minWidth: 50,
    minHeight: 50,
  },
  eventDay: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    lineHeight: 20,
  },
  eventMonth: {
    fontSize: 10,
    color: 'white',
    textTransform: 'uppercase',
    fontWeight: 'bold',
  },
  cardContent: {
    flex: 1,
  },
  eventTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 5,
    textAlign: 'right',
  },
  eventDescription: {
    fontSize: 12,
    color: '#7f8c8d',
    lineHeight: 16,
    marginBottom: 8,
    textAlign: 'right',
  },
  eventMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 15,
    flexWrap: 'wrap',
  },
  metaText: {
    fontSize: 11,
    color: '#bdc3c7',
  },
  fab: {
    position: 'absolute',
    bottom: 30,
    right: 30,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#667eea',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  fabText: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
});
