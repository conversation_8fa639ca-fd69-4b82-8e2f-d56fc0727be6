import { designTokens, Spacing<PERSON><PERSON>, <PERSON><PERSON><PERSON>, BorderRadius<PERSON><PERSON>, <PERSON>K<PERSON> } from './designTokens';

// Utility functions for accessing design tokens

/**
 * Get spacing value by key
 */
export const getSpacing = (key: SpacingKey): number => {
  return designTokens.spacing[key];
};

/**
 * Get multiple spacing values
 */
export const getSpacings = (...keys: SpacingKey[]): number[] => {
  return keys.map(key => designTokens.spacing[key]);
};

/**
 * Get color value by key and shade
 */
export const getColor = (key: ColorKey, shade?: number): string => {
  const color = designTokens.colors[key];
  
  if (typeof color === 'string') {
    return color;
  }
  
  if (typeof color === 'object' && shade) {
    return color[shade as keyof typeof color] || color[500];
  }
  
  // Default to 500 shade for color objects
  if (typeof color === 'object') {
    return color[500] || Object.values(color)[0];
  }
  
  return '#000000'; // Fallback
};

/**
 * Get border radius value by key
 */
export const getBorderRadius = (key: BorderRadius<PERSON>ey): number => {
  return designTokens.borderRadius[key];
};

/**
 * Get shadow style by key
 */
export const getShadow = (key: ShadowKey) => {
  return designTokens.shadows[key];
};

/**
 * Get typography style by combining font size, line height, and weight
 */
export const getTypography = (
  size: keyof typeof designTokens.typography.fontSize,
  weight?: keyof typeof designTokens.typography.fontWeight,
  lineHeight?: keyof typeof designTokens.typography.lineHeight
) => {
  return {
    fontSize: designTokens.typography.fontSize[size],
    fontWeight: weight ? designTokens.typography.fontWeight[weight] : designTokens.typography.fontWeight.normal,
    lineHeight: lineHeight ? designTokens.typography.lineHeight[lineHeight] : designTokens.typography.lineHeight[size] || designTokens.typography.lineHeight.base,
  };
};

/**
 * Create responsive styles based on screen width
 */
export const createResponsiveStyle = (
  baseStyle: any,
  breakpointStyles: Partial<Record<keyof typeof designTokens.breakpoints, any>>
) => {
  // This would need to be implemented with a responsive hook
  // For now, return base style
  return baseStyle;
};

/**
 * Combine multiple shadows
 */
export const combineShadows = (...shadowKeys: ShadowKey[]) => {
  // For React Native, we can only use one shadow at a time
  // Return the largest shadow
  const shadows = shadowKeys.map(key => designTokens.shadows[key]);
  return shadows.reduce((largest, current) => {
    return current.elevation > largest.elevation ? current : largest;
  });
};

/**
 * Create a color with opacity
 */
export const getColorWithOpacity = (colorKey: ColorKey, shade: number, opacity: number): string => {
  const color = getColor(colorKey, shade);
  
  // Convert hex to rgba
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  
  return color;
};

/**
 * Get animation duration
 */
export const getAnimationDuration = (speed: keyof typeof designTokens.animation): number => {
  return designTokens.animation[speed];
};

/**
 * Get z-index value
 */
export const getZIndex = (layer: keyof typeof designTokens.zIndex): number | string => {
  return designTokens.zIndex[layer];
};

/**
 * Create consistent button styles
 */
export const createButtonStyle = (
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' = 'primary',
  size: 'sm' | 'md' | 'lg' = 'md'
) => {
  const baseStyle = {
    borderRadius: getBorderRadius('md'),
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    flexDirection: 'row' as const,
  };

  const sizeStyles = {
    sm: {
      paddingHorizontal: getSpacing('md'),
      paddingVertical: getSpacing('sm'),
      ...getTypography('sm', 'medium'),
    },
    md: {
      paddingHorizontal: getSpacing('lg'),
      paddingVertical: getSpacing('md'),
      ...getTypography('base', 'medium'),
    },
    lg: {
      paddingHorizontal: getSpacing('xl'),
      paddingVertical: getSpacing('lg'),
      ...getTypography('lg', 'medium'),
    },
  };

  const variantStyles = {
    primary: {
      backgroundColor: getColor('primary'),
      color: getColor('white'),
      ...getShadow('sm'),
    },
    secondary: {
      backgroundColor: getColor('secondary'),
      color: getColor('white'),
      ...getShadow('sm'),
    },
    outline: {
      backgroundColor: getColor('transparent'),
      borderWidth: 1,
      borderColor: getColor('primary'),
      color: getColor('primary'),
    },
    ghost: {
      backgroundColor: getColor('transparent'),
      color: getColor('primary'),
    },
  };

  return {
    ...baseStyle,
    ...sizeStyles[size],
    ...variantStyles[variant],
  };
};

/**
 * Create consistent card styles
 */
export const createCardStyle = (elevated: boolean = true) => {
  return {
    backgroundColor: getColor('white'),
    borderRadius: getBorderRadius('lg'),
    padding: getSpacing('lg'),
    ...(elevated ? getShadow('base') : {}),
  };
};
