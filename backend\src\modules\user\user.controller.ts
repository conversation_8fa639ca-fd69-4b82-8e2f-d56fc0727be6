import { Request, Response } from 'express';
import { UserService } from './user.service';
import { CreateUserDTO } from './user.types';
import { getModuleLogger } from '../../utils/logger';
import { getSupabaseClient } from '../../config/supabase';
import { Prisma } from '@prisma/client';

const logger = getModuleLogger('User');

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  private validateCreateUserData(data: any): { isValid: boolean; error?: string } {
    // Check required fields
    const requiredFields = ['email', 'password', 'role', 'userType'];
    const missingFields = requiredFields.filter(field => !data[field]);
    if (missingFields.length > 0) {
      return {
        isValid: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return {
        isValid: false,
        error: 'Invalid email format'
      };
    }

    // Validate role
    if (!['ADMIN', 'MODERATOR', 'USER'].includes(data.role)) {
      return {
        isValid: false,
        error: 'Invalid role'
      };
    }

    // Validate userType
    if (!['ADULT', 'YOUTH', 'CHILD', 'EXTERNAL'].includes(data.userType)) {
      return {
        isValid: false,
        error: 'Invalid user type'
      };
    }

    return { isValid: true };
  }

  private validateCreateUserProfileData(data: any): { isValid: boolean; error?: string } {
    // Check required fields
    const requiredFields = ['id', 'email', 'role', 'userType'];
    const missingFields = requiredFields.filter(field => !data[field]);
    if (missingFields.length > 0) {
      return {
        isValid: false,
        error: `Missing required fields: ${missingFields.join(', ')}`
      };
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      return {
        isValid: false,
        error: 'Invalid email format'
      };
    }

    // Validate role if provided
    if (data.role && !['ADMIN', 'MODERATOR', 'USER'].includes(data.role)) {
      return {
        isValid: false,
        error: 'Invalid role'
      };
    }

    // Validate userType if provided
    if (data.userType && !['ADULT', 'YOUTH', 'CHILD', 'EXTERNAL'].includes(data.userType)) {
      return {
        isValid: false,
        error: 'Invalid user type'
      };
    }

    return { isValid: true };
  }

  async createUser(req: Request, res: Response): Promise<void> {
    try {
      // TODO: Temporarily removed admin check - will be reimplemented in the future
      // if (req.user?.role !== 'ADMIN') {
      //   res.status(403).json({ success: false, error: 'Not authorized to create users' });
      //   return;
      // }
      const validation = this.validateCreateUserData(req.body);
      if (!validation.isValid) {
        res.status(400).json({ success: false, error: validation.error });
        return;
      }
      const user = await this.userService.createUser(req.body as CreateUserDTO);
      res.status(201).json({ success: true, data: user });
    } catch (error) {
      logger.error(`Error in createUser: ${error}`);
      res.status(500).json({ success: false, error: 'Failed to create user' });
    }
  }

  async createUserProfile(req: Request, res: Response): Promise<void> {
    try {
      logger.debug('Creating user profile', {
        body: req.body,
        headers: req.headers,
        user: req.user
      });

      // Verify JWT token
      const token = req.headers.authorization?.split(' ')[1];
      if (!token) {
        logger.error('No token provided in createUserProfile');
        res.status(401).json({ error: 'No token provided' });
        return;
      }

      logger.debug('Verifying token with Supabase');
      const supabase = getSupabaseClient();
      const { data: { user }, error: verifyError } = await supabase.auth.getUser(token);
      if (verifyError) {
        logger.error('Token verification failed', { error: verifyError.message });
        res.status(401).json({ error: 'Invalid token' });
        return;
      }

      if (!user) {
        logger.error('No user found in token verification');
        res.status(401).json({ error: 'User not found in token' });
        return;
      }

      logger.debug('Token verified successfully', {
        userId: user.id,
        email: user.email
      });

      // Validate data
      const validationResult = this.validateCreateUserProfileData(req.body);
      if (!validationResult.isValid) {
        logger.error('Invalid user profile data', { error: validationResult.error });
        res.status(400).json({ error: validationResult.error });
        return;
      }

      // Ensure user ID in request matches token
      if (req.body.id !== user.id) {
        logger.error('User ID mismatch', {
          requestId: req.body.id,
          tokenId: user.id
        });
        res.status(400).json({ error: 'User ID in request does not match token' });
        return;
      }

      logger.debug('Creating user profile in database', {
        userId: user.id,
        email: user.email
      });

      // Create user profile
      const userProfile = await this.userService.createUserProfile(req.body);
      
      logger.debug('User profile created successfully', {
        userId: userProfile.id,
        email: userProfile.email
      });

      res.status(201).json({
        success: true,
        data: userProfile
      });
    } catch (error) {
      logger.error('Error creating user profile', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });
      res.status(500).json({ error: 'Failed to create user profile' });
    }
  }

  async updateUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      const userExists = await this.userService.getUserById(id);
      if (!userExists) {
        res.status(404).json({ success: false, error: 'User not found' });
        return;
      }
      // Only check if user is updating their own profile
      if (req.user?.id !== id) {
        res.status(403).json({ success: false, error: 'Not authorized to update this user' });
        return;
      }
      const user = await this.userService.updateUser(id, req.body);
      res.json({ success: true, data: user });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        res.status(404).json({ success: false, error: 'User not found' });
        return;
      }
      logger.error(`Error in updateUser: ${error}`);
      res.status(500).json({ success: false, error: 'Failed to update user' });
    }
  }

  async getUserById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
        return;
      }

      logger.debug(`Fetching user profile for ID: ${id}`);
      const user = await this.userService.getUserById(id);

      if (!user) {
        logger.warn(`User not found for ID: ${id}`);
        res.status(404).json({
          success: false,
          error: 'User not found'
        });
        return;
      }

      logger.debug(`User profile found:`, {
        id: user.id,
        email: user.email,
        role: user.role,
        user_type: user.user_type,
        first_name: user.first_name,
        last_name: user.last_name
      });

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      logger.error(`Error in getUserById: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch user'
      });
    }
  }

  async getAllUsers(_req: Request, res: Response): Promise<void> {
    try {
      const users = await this.userService.getAllUsers();
      res.json({ success: true, data: users });
    } catch (error) {
      logger.error(`Error in getAllUsers: ${error}`);
      res.status(500).json({ success: false, error: 'Failed to get users' });
    }
  }

  async getUserCount(_req: Request, res: Response): Promise<void> {
    try {
      const count = await this.userService.getUserCount();
      res.json({
        success: true,
        data: count
      });
    } catch (error) {
      logger.error(`Error in getUserCount: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to get user count'
      });
    }
  }

  async getUsersByTypeOrRole(_req: Request, res: Response): Promise<void> {
    try {
      const users = await this.userService.getUsersByTypeOrRole();
      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      logger.error(`Error in getUsersByTypeOrRole: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch users by type or role'
      });
    }
  }

  async deleteUser(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({ success: false, error: 'User ID is required' });
        return;
      }
      const userExists = await this.userService.getUserById(id);
      if (!userExists) {
        res.status(404).json({ success: false, error: 'User not found' });
        return;
      }
      await this.userService.deleteUser(id);
      res.json({ success: true, message: 'User deleted successfully' });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        res.status(404).json({ success: false, error: 'User not found' });
        return;
      }
      logger.error(`Error in deleteUser: ${error}`);
      res.status(500).json({ success: false, error: 'Failed to delete user' });
    }
  }

  async updateUIPreferences(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
        return;
      }

      // Only allow users to update their own preferences
      if (req.user?.id !== id) {
        res.status(403).json({
          success: false,
          error: 'Not authorized to update these preferences'
        });
        return;
      }

      const { uiPreferences } = req.body;
      if (!uiPreferences) {
        res.status(400).json({
          success: false,
          error: 'UI preferences are required'
        });
        return;
      }

      const user = await this.userService.updateUIPreferences(id, uiPreferences);
      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      logger.error(`Error in updateUIPreferences: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to update UI preferences'
      });
    }
  }

  async getUserUIPreferences(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      if (!id) {
        res.status(400).json({
          success: false,
          error: 'User ID is required'
        });
        return;
      }

      const preferences = await this.userService.getUserUIPreferences(id);
      if (!preferences) {
        res.status(404).json({
          success: false,
          error: 'UI preferences not found'
        });
        return;
      }

      res.json({
        success: true,
        data: preferences
      });
    } catch (error) {
      logger.error(`Error in getUserUIPreferences: ${error}`);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch UI preferences'
      });
    }
  }
}