import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import Ionicons from 'react-native-vector-icons/Ionicons';

export type IconSet = 'MaterialIcons' | 'MaterialCommunityIcons' | 'FontAwesome' | 'Ionicons';

interface IconProps {
  name: string;
  size?: number;
  color?: string;
  iconSet?: IconSet;
  style?: StyleProp<ViewStyle>;
}

const Icon: React.FC<IconProps> = ({
  name,
  size = 24,
  color = '#000',
  iconSet = 'MaterialIcons',
  style,
}) => {
  const getIconComponent = () => {
    switch (iconSet) {
      case 'MaterialCommunityIcons':
        return MaterialCommunityIcons;
      case 'FontAwesome':
        return FontAwesome;
      case 'Ionicons':
        return Ionicons;
      default:
        return MaterialIcons;
    }
  };

  const IconComponent = getIconComponent();

  return (
    <IconComponent
      name={name}
      size={size}
      color={color}
      style={style}
    />
  );
};

export default Icon;
