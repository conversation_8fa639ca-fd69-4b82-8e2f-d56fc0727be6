import React from 'react';
import { Platform, Text } from 'react-native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { useNavigation } from '@react-navigation/native';
import { TAB_ICONS } from '@constants';
import { useAuth } from '@contexts/AuthContext';
import { RootStackParamList, TabParamList } from '@navigation/types';
import HomeScreen from '@screens/home/<USER>';
import EventsNavigator from '@navigation/EventsNavigator';
import JobsNavigator from '@navigation/JobsNavigator';
import PeopleNavigator from '@navigation/PeopleNavigator';
import ChatNavigator from '@navigation/ChatNavigator';
import DebugScreen from '@screens/debug/DebugScreen';
import LoginScreen from '@screens/auth/LoginScreen';
import RegisterScreen from '@screens/auth/RegisterScreen';
import LoadingScreen from '@screens/auth/LoadingScreen';
import ProfileScreen from '@screens/profile/ProfileScreen';
import { getColor } from '../theme';
import Icon from '../components/Icon';

const Stack = createNativeStackNavigator<RootStackParamList>();
const Tab = createBottomTabNavigator<TabParamList>();

const MainTabs = () => {
  const navigation = useNavigation();
  const { user } = useAuth();
  const isAdmin = user?.role === 'ADMIN';

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarIcon: ({ color, size = 24 }) => {
          const state = navigation.getState();
          const currentRoute = state?.routes[state?.index ?? 0];
          const routeName = currentRoute?.name as keyof typeof TAB_ICONS;

          if (routeName && TAB_ICONS[routeName]) {
            const { name, emoji } = TAB_ICONS[routeName];
            return (
              <Icon
                name={name}
                iconSet="MaterialIcons"
                size={size}
                color={color}
                emoji={emoji}
                useEmoji={true}
              />
            );
          }

          // Fallback for unknown routes
          return <Text style={{ fontSize: size, color }}>?</Text>;
        },
        tabBarActiveTintColor: getColor('primary'),
        tabBarInactiveTintColor: getColor('neutral', 700),
        headerShown: false,
      }}
    >
      <Tab.Screen name="Home" component={HomeScreen} />
      <Tab.Screen name="Events" component={EventsNavigator} />
      <Tab.Screen name="Jobs" component={JobsNavigator} />
      <Tab.Screen name="People" component={PeopleNavigator} />
      <Tab.Screen name="Chat" component={ChatNavigator} />
      {isAdmin && <Tab.Screen name="Debug" component={DebugScreen} />}
    </Tab.Navigator>
  );
};

const AppNavigator = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return <LoadingScreen />;
  }

  return (
    <Stack.Navigator screenOptions={{ headerShown: false }}>
      {user ? (
        <>
          <Stack.Screen name="Main" component={MainTabs} />
          <Stack.Screen name="Profile" component={ProfileScreen} />
        </>
      ) : (
        <>
          <Stack.Screen name="Login" component={LoginScreen} />
          <Stack.Screen name="Register" component={RegisterScreen} />
        </>
      )}
    </Stack.Navigator>
  );
};

export default AppNavigator;
