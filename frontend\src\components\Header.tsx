import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  StatusBar,
  Platform,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from 'react-native-paper';
import { getTextAlign } from '@utils';
import { useThemeStyles } from '@hooks';
import { createHeaderButtonStyle, getShadow } from '../theme';

interface HeaderProps {
  title: string;
  showBackButton?: boolean;
  rightComponent?: React.ReactNode;
  onBackPress?: () => void;
}

export default function Header({
  title,
  showBackButton = false,
  rightComponent,
  onBackPress
}: HeaderProps) {
  const navigation = useNavigation();
  const theme = useTheme();
  const { textStyles, spacing } = useThemeStyles();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.header, { backgroundColor: theme.colors.primary, paddingHorizontal: spacing.md, paddingVertical: spacing.md }]}>
      <StatusBar barStyle="light-content" backgroundColor={theme.colors.primary} />

      {/* Left side - Back button or spacer */}
      <View style={styles.leftContainer}>
        {showBackButton ? (
          <TouchableOpacity style={createHeaderButtonStyle()} onPress={handleBackPress}>
            <Text style={[textStyles.h6, { color: theme.colors.onPrimary }]}>←</Text>
          </TouchableOpacity>
        ) : (
          <View style={styles.spacer} />
        )}
      </View>

      {/* Center - Title */}
      <View style={styles.centerContainer}>
        <Text style={[textStyles.h6, { color: theme.colors.onPrimary, textAlign: getTextAlign() }]}>
          {title}
        </Text>
      </View>

      {/* Right side - Custom component or spacer */}
      <View style={styles.rightContainer}>
        {rightComponent || <View style={styles.spacer} />}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: Platform.OS === 'android' ? (StatusBar.currentHeight || 0) + 15 : 15,
    ...getShadow('md'),
  },
  leftContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  centerContainer: {
    flex: 2,
    alignItems: 'center',
  },
  rightContainer: {
    flex: 1,
    alignItems: 'flex-end',
  },
  spacer: {
    width: 40,
  },
});
