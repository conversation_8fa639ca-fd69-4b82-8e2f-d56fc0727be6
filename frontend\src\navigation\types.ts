export type RootStackParamList = {
  Main: undefined;
  Login: undefined;
  Register: undefined;
  Loading: undefined;
  Profile: undefined;
};

export type TabParamList = {
  Home: undefined;
  Events: undefined;
  Jobs: undefined;
  People: undefined;
  Chat: undefined;
  Debug: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
};

// MainTabParamList removed - using local TabParamList in AppNavigator instead

export type EventsStackParamList = {
  EventsList: undefined;
  EventDetails: { eventId: string };
  CreateEvent: undefined;
  EditEvent: { eventId: string };
};

export type ChatStackParamList = {
  ChatList: undefined;
  ChatRoom: { roomId: string };
  CreateGroup: undefined;
};

export type JobsStackParamList = {
  JobsList: undefined;
  JobDetails: { jobId: string };
  CreateJob: undefined;
  MyJobs: undefined;
};

export type CommercialUnionsStackParamList = {
  UnionsList: undefined;
  UnionDetails: { unionId: string };
  CreateUnion: undefined;
  MyUnions: undefined;
};

export type SurveysStackParamList = {
  SurveysList: undefined;
  SurveyDetails: { surveyId: string };
  CreateSurvey: undefined;
  SurveyResults: { surveyId: string };
};

export type PeopleStackParamList = {
  PeopleDirectory: undefined;
};
